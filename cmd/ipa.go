/*
Copyright © 2025 NAME HERE <EMAIL ADDRESS>
*/
package cmd

import (
	"fmt"
	"onetools/cmd/ipa"
	"onetools/cmd/utility"
	"os"
	"strings"

	"github.com/spf13/cobra"
)

var IPAPath string
var OutputPath string
var IconPaths string
var UnrealProjectPath string

// resign command variables
var BundleID string
var ProvisioningProfile string
var Certificate string
var DisplayName string
var AppVersion string
var BuildVersion string
var AppIconPath string
var LaunchImagePath string
var ForceSwiftUpdate bool
var EntitlementsPath string
var KeepTempDir bool
var AdditionalPlistPath string
var RemovePlistKeys string
var DeleteFilePaths string
var AddFilesPath string

// ipaCmd represents the ipa command
var ipaCmd = &cobra.Command{
	Use:   "ipa",
	Short: "处理iOS应用包文件",
	Long: `ipa命令用于处理iOS应用包文件，支持以下操作：
  unzip   解压ipa文件
  swiftSupport  修复ipa中SwiftSupport
  alt_icons  生成iOS应用的备用图标
  resign  重新签名ipa文件

示例：
  onetools ipa unzip -p /path/to/app.ipa -o /path/to/output
  onetools ipa swiftSupport -p /path/to/app.ipa
  onetools ipa alt_icons --icons /path/to/icon1.png,/path/to/icon2.png --project /path/to/UnrealProject
  onetools ipa resign -p /path/to/app.ipa --bundle-id com.example.app --provision /path/to/profile.mobileprovision`,
}

// unzip command
var unzip = &cobra.Command{
	Use:   "unzip",
	Short: "解压ipa文件",
	Long:  `解压指定的ipa文件到目标目录`,
	Run:   unzipExecute,
}

// swiftSupport command
var swiftSupport = &cobra.Command{
	Use:   "swiftSupport",
	Short: "修复ipa中SwiftSupport",
	Long:  `修复ipa文件中的SwiftSupport库文件`,
	Run:   fixSwiftSupportExecute,
}

// alt_icons command
var alternateIcons = &cobra.Command{
	Use:   "alt_icons",
	Short: "生成iOS应用的备用图标",
	Long: `生成iOS应用的备用图标，支持Unity和Unreal Engine项目集成
要求图标尺寸为1024x1024像素，支持PNG和JPEG格式

示例：
  # 基本用法
  onetools ipa alt_icons --icons /path/to/icon1.png,/path/to/icon2.png,/path/to/icon3.png
  
  # 指定输出目录
  onetools ipa alt_icons --icons /path/to/icon1.png,/path/to/icon2.png --output /path/to/output
  
  # 集成到 Unity 项目
  onetools ipa alt_icons --icons /path/to/icon1.png,/path/to/icon2.png --project /path/to/UnityProject
  
  # 集成到 Unreal 项目
  onetools ipa alt_icons --icons /path/to/icon1.png,/path/to/icon2.png --project /path/to/UnrealProject`,
	Run: alternateIconsExecute,
}

// resign command
var resign = &cobra.Command{
	Use:   "resign",
	Short: "重新签名ipa文件",
	Long: `重新签名ipa文件，支持替换Bundle ID、mobileprovision、证书等
支持替换显示名称、版本号、应用图标和启动图片
包含动态库时会自动重签动态库，Distribution包含Swift库时支持强制更新SwiftSupport
支持自定义entitlements.plist文件，如不指定则从mobileprovision中提取
支持合并附加的plist文件到Info.plist中
支持删除Info.plist中指定的最外层key
支持删除.app目录中指定的文件或文件夹

示例：
  # 基本重签名
  onetools ipa resign -p /path/to/app.ipa --bundle-id com.example.newapp
  
  # 完整重签名
  onetools ipa resign -p /path/to/app.ipa \
    --bundle-id com.example.newapp \
    --provision /path/to/profile.mobileprovision \
    --cert "iPhone Developer: Your Name (XXXXXXXXXX)" \
    --display-name "New App Name" \
    --app-version "2.0.0" \
    --build-version "200" \
    --app-icon /path/to/icon.png \
    --launch-image /path/to/launch.png \
    --entitlements /path/to/entitlements.plist \
    --additional-plist /path/to/additional.plist \
    --remove-plist-keys "key1,key2,key3" \
    --delete-files "Frameworks/unused.framework,resources/large_video.mp4" \
    --add-files-path /path/to/additional/files \
    --force-swift-update \
    --keep-temp-dir \
    -o /path/to/output.ipa`,
	Run: resignExecute,
}

func init() {
	rootCmd.AddCommand(ipaCmd)

	// Add subcommands
	ipaCmd.AddCommand(unzip)
	ipaCmd.AddCommand(swiftSupport)
	ipaCmd.AddCommand(alternateIcons)
	ipaCmd.AddCommand(resign)

	// Add flags to unzip command
	unzip.Flags().StringVarP(&IPAPath, "path", "p", "", `【必传】ipa文件路径`)
	unzip.Flags().StringVarP(&OutputPath, "output", "o", "", `【可选】执行完成后，输出目标文件路径`)

	// Add flags to swiftSupport command
	swiftSupport.Flags().StringVarP(&IPAPath, "path", "p", "", `【必传】ipa文件路径`)
	swiftSupport.Flags().BoolP("forceUpdate", "f", false, `【可选】强制更新SwiftSupport文件`)

	// Add flags to alternate_icons command
	alternateIcons.Flags().StringVar(&IconPaths, "icons", "", `【必传】图标文件路径，多个文件用逗号分隔`)
	alternateIcons.Flags().StringVar(&UnrealProjectPath, "project", "", `【可选】Unreal项目路径`)
	alternateIcons.Flags().StringVarP(&OutputPath, "output", "o", "", `【可选】输出目录路径`)

	// Add flags to resign command
	resign.Flags().StringVarP(&IPAPath, "path", "p", "", `【必传】ipa文件路径`)
	resign.Flags().StringVarP(&OutputPath, "output", "o", "", `【可选】输出ipa文件路径`)
	resign.Flags().StringVar(&BundleID, "bundle-id", "", `【可选】新的Bundle ID`)
	resign.Flags().StringVar(&ProvisioningProfile, "provision", "", `【可选】mobileprovision文件路径`)
	resign.Flags().StringVar(&Certificate, "cert", "", `【可选】证书名称，如"iPhone Developer: Your Name (XXXXXXXXXX)"或"iPhone Distribution"`)
	resign.Flags().StringVar(&DisplayName, "display-name", "", `【可选】应用显示名称`)
	resign.Flags().StringVar(&AppVersion, "app-version", "", `【可选】应用版本号`)
	resign.Flags().StringVar(&BuildVersion, "build-version", "", `【可选】构建版本号`)
	resign.Flags().StringVar(&AppIconPath, "app-icon", "", `【可选】应用图标文件路径`)
	resign.Flags().StringVar(&LaunchImagePath, "launch-image", "", `【可选】启动图片文件路径`)
	resign.Flags().BoolVar(&ForceSwiftUpdate, "force-swift-update", false, `【可选】强制更新SwiftSupport，默认会根据原包中是否包含SwiftSupport进行自动处理`)
	resign.Flags().StringVar(&EntitlementsPath, "entitlements", "", `【可选】自定义entitlements.plist文件路径`)
	resign.Flags().BoolVar(&KeepTempDir, "keep-temp-dir", false, `【可选】保留临时文件目录，用于调试，默认为false（处理完成后删除临时文件）`)
	resign.Flags().StringVar(&AdditionalPlistPath, "additional-plist", "", `【可选】附加的plist文件路径，将合并到app/Info.plist中`)
	resign.Flags().StringVar(&RemovePlistKeys, "remove-plist-keys", "", `【可选】要从Info.plist中删除的最外层key，多个key用逗号分隔`)
	resign.Flags().StringVarP(&AddFilesPath, "add-files-path", "a", "", `【可选】要添加到.app目录的文件夹路径，该文件夹内的文件结构对应IPA解压后.app目录的根目录结构 (注：当添加Resources目录后会导致重签失败)`)
	resign.Flags().StringVar(&DeleteFilePaths, "delete-files", "", `【可选】要从.app目录中删除的文件或文件夹路径，多个路径用逗号分隔，路径以.app根目录为基准`)
}

func unzipExecute(cmd *cobra.Command, args []string) {
	if OutputPath == "" {
		pwdPath, _ := os.Getwd()
		OutputPath = pwdPath
	}

	// 确保IPAPath是绝对路径
	absIPAPath, _ := utility.EnsureAbsolutePath(IPAPath)
	IPAPath = absIPAPath

	if !utility.IsExist(IPAPath) {
		panic(fmt.Sprintf("【%s】路径不存在，请确认IPA路径是否正确", IPAPath))
	}

	// 确保OutputPath是绝对路径
	absOutputPath, _ := utility.EnsureAbsolutePath(OutputPath)
	OutputPath = absOutputPath

	// 解压IPA文件
	fmt.Println("开始解压IPA文件...")
	err := ipa.UnzipIPAExe(IPAPath, OutputPath)
	// 输出err
	if err != nil {
		panic(fmt.Sprintf("解压IPA文件失败: %v", err))
	} else {
		fmt.Printf("解压完成，路径：%s\n", OutputPath)
	}
}

// 修改ipa包中的SwiftSupport文件
func fixSwiftSupportExecute(cmd *cobra.Command, args []string) {
	// 确保IPAPath是绝对路径
	absIPAPath, _ := utility.EnsureAbsolutePath(IPAPath)
	IPAPath = absIPAPath

	if !utility.IsExist(IPAPath) {
		panic(fmt.Sprintf("【%s】路径不存在，请确认IPA路径是否正确", IPAPath))
	}

	forceUpdate, _ := cmd.Flags().GetBool("forceUpdate")
	ipa.FixSwiftSupportExe(IPAPath, forceUpdate)
}

// 生成iOS应用的备用图标
func alternateIconsExecute(cmd *cobra.Command, args []string) {
	if IconPaths == "" {
		fmt.Println("错误：必须指定图标文件路径")
		fmt.Println("使用方法：onetools ipa alt_icons --icons /path/to/icon1.png,/path/to/icon2.png")
		return
	}

	// Parse icon paths
	var iconPathsList []string
	if strings.Contains(IconPaths, ",") {
		iconPathsList = strings.Split(IconPaths, ",")
	} else {
		iconPathsList = []string{IconPaths}
	}

	// Trim whitespace and ensure absolute paths
	for i := range iconPathsList {
		iconPathsList[i] = strings.TrimSpace(iconPathsList[i])
		// 确保图标路径是绝对路径
		absPath, _ := utility.EnsureAbsolutePath(iconPathsList[i])
		iconPathsList[i] = absPath
	}

	if len(iconPathsList) == 0 {
		fmt.Println("错误：至少需要指定一个图标文件")
		return
	}

	// 确保项目路径是绝对路径
	if UnrealProjectPath != "" {
		absProjectPath, _ := utility.EnsureAbsolutePath(UnrealProjectPath)
		UnrealProjectPath = absProjectPath
	}

	// 确保输出路径是绝对路径
	if OutputPath != "" {
		absOutputPath, _ := utility.EnsureAbsolutePath(OutputPath)
		OutputPath = absOutputPath
	}

	// Execute the alternate icons generation
	err := ipa.GenerateAlternateIconsExe(iconPathsList, UnrealProjectPath, OutputPath)
	if err != nil {
		fmt.Printf("生成备用图标失败: %v\n", err)
		os.Exit(1)
	}
}

// 重新签名ipa文件
func resignExecute(cmd *cobra.Command, args []string) {
	if IPAPath == "" {
		fmt.Println("错误：必须指定ipa文件路径")
		fmt.Println("使用方法：onetools ipa resign -p /path/to/app.ipa")
		return
	}

	// 确保IPAPath是绝对路径
	absIPAPath, _ := utility.EnsureAbsolutePath(IPAPath)
	IPAPath = absIPAPath

	if !utility.IsExist(IPAPath) {
		panic(fmt.Sprintf("【%s】路径不存在，请确认IPA路径是否正确", IPAPath))
	}

	// 确保其他路径参数是绝对路径
	if OutputPath != "" {
		absOutputPath, _ := utility.EnsureAbsolutePath(OutputPath)
		OutputPath = absOutputPath
	}

	if ProvisioningProfile != "" {
		absProvisioningProfile, _ := utility.EnsureAbsolutePath(ProvisioningProfile)
		ProvisioningProfile = absProvisioningProfile
	}

	if AppIconPath != "" {
		absAppIconPath, _ := utility.EnsureAbsolutePath(AppIconPath)
		AppIconPath = absAppIconPath
	}

	if LaunchImagePath != "" {
		absLaunchImagePath, _ := utility.EnsureAbsolutePath(LaunchImagePath)
		LaunchImagePath = absLaunchImagePath
	}

	if EntitlementsPath != "" {
		absEntitlementsPath, _ := utility.EnsureAbsolutePath(EntitlementsPath)
		EntitlementsPath = absEntitlementsPath
	}

	// 确保附加的plist文件路径是绝对路径
	if AdditionalPlistPath != "" {
		absAdditionalPlistPath, _ := utility.EnsureAbsolutePath(AdditionalPlistPath)
		AdditionalPlistPath = absAdditionalPlistPath
	}

	if AddFilesPath != "" {
		absAddFilesPath, _ := utility.EnsureAbsolutePath(AddFilesPath)
		AddFilesPath = absAddFilesPath
	}

	// 创建重签名配置
	config := &ipa.ResignConfig{
		IPAPath:             IPAPath,
		OutputPath:          OutputPath,
		BundleID:            BundleID,
		ProvisioningProfile: ProvisioningProfile,
		Certificate:         Certificate,
		DisplayName:         DisplayName,
		AppVersion:          AppVersion,
		BuildVersion:        BuildVersion,
		AppIconPath:         AppIconPath,
		LaunchImagePath:     LaunchImagePath,
		ForceSwiftUpdate:    ForceSwiftUpdate,
		EntitlementsPath:    EntitlementsPath,
		KeepTempDir:         KeepTempDir,
		AdditionalPlistPath: AdditionalPlistPath,
		RemovePlistKeys:     RemovePlistKeys,
		DeleteFilePaths:     DeleteFilePaths,
		AddFilesPath:        AddFilesPath,
	}

	// 执行重签名
	err := ipa.ResignIPAExe(config)
	if err != nil {
		fmt.Printf("重签名失败: %v\n", err)
		os.Exit(1)
	}
}
